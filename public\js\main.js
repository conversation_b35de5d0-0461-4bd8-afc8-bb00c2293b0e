// Register Service Worker for PWA functionality
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/service-worker.js')
            .then(registration => {
                console.log('Service Worker registered with scope:', registration.scope);
            })
            .catch(error => {
                console.error('Service Worker registration failed:', error);
            });
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // Theme Toggle Functionality
    const themeToggle = document.getElementById('theme-toggle-checkbox');

    // Check for saved theme preference or use preferred color scheme
    const currentTheme = localStorage.getItem('theme') ||
                        (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');

    // Apply the saved theme or default
    if (currentTheme === 'dark') {
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggle.checked = true;
    }

    // Toggle theme when checkbox is clicked
    themeToggle.addEventListener('change', function() {
        if (this.checked) {
            document.documentElement.setAttribute('data-theme', 'dark');
            localStorage.setItem('theme', 'dark');
        } else {
            document.documentElement.removeAttribute('data-theme');
            localStorage.setItem('theme', 'light');
        }
    });
    // Mobile Navigation Toggle
    const hamburger = document.querySelector('.hamburger');
    const navLinks = document.querySelector('.nav-links');

    if (hamburger) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navLinks.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on a nav link
    const navItems = document.querySelectorAll('.nav-links a');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            hamburger.classList.remove('active');
            navLinks.classList.remove('active');
        });
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 80,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Contact Form Submission
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                message: document.getElementById('message').value
            };

            // Show loading indicator or disable submit button
            const submitBtn = contactForm.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = 'Sending...';

            try {
                // Send email using EmailJS
                const emailjsResult = await emailjs.send(
                    'ouru.dev_contactmail', // EmailJS service ID
                    'ouru.dev_template_id', // EmailJS template ID
                    {
                        name: formData.name,
                        email: formData.email,
                        message: formData.message,
                        // You can add additional parameters here if needed
                        subject: 'New Contact Form Submission'
                    }
                );

                console.log('EmailJS SUCCESS!', emailjsResult.status, emailjsResult.text);

                // Also send to backend for storage (optional)
                try {
                    const response = await fetch(`${window.API_CONFIG.BASE_URL}/contact`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                    const data = await response.json();
                    console.log('Backend storage:', data);
                } catch (backendError) {
                    // If backend storage fails, just log the error but don't show to user
                    // since the email was already sent successfully
                    console.error('Backend storage error:', backendError);
                }

                // Show success message
                alert('Message sent successfully!');
                contactForm.reset();
            } catch (error) {
                console.error('Error:', error);
                alert('Failed to send message. Please try again.');
            } finally {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText;
            }
        });
    }

    // Fetch Projects
    const fetchProjects = async () => {
        try {
            const response = await fetch(`${window.API_CONFIG.BASE_URL}/projects/featured`);
            const projects = await response.json();

            const projectsContainer = document.getElementById('projects-container');

            // If we want to dynamically load projects, uncomment this
            // if (projectsContainer && projects.length > 0) {
            //     projectsContainer.innerHTML = '';
            //
            //     projects.forEach(project => {
            //         const projectCard = createProjectCard(project);
            //         projectsContainer.appendChild(projectCard);
            //     });
            // }
        } catch (error) {
            console.error('Error fetching projects:', error);
        }
    };

    // Create Project Card Element
    const createProjectCard = (project) => {
        const card = document.createElement('div');
        card.className = 'project-card';

        card.innerHTML = `
            <div class="project-image">
                <img src="${project.image}" alt="${project.title}">
            </div>
            <div class="project-content">
                <h3>${project.title}</h3>
                <p>${project.description}</p>
                <div class="project-tech">
                    ${project.technologies.map(tech => `<span>${tech}</span>`).join('')}
                </div>
                <div class="project-links">
                    <a href="${project.githubLink}" target="_blank"><i class="fab fa-github"></i> Code</a>
                    ${project.liveLink ? `<a href="${project.liveLink}" target="_blank"><i class="fas fa-external-link-alt"></i> Live</a>` : ''}
                </div>
            </div>
        `;

        return card;
    };

    // Fetch Skills
    const fetchSkills = async () => {
        try {
            const response = await fetch(`${window.API_CONFIG.BASE_URL}/skills`);
            const skills = await response.json();

            // If we want to dynamically load skills, uncomment this
            // const frontendSkills = skills.filter(skill => skill.category === 'frontend');
            // const backendSkills = skills.filter(skill => skill.category === 'backend');
            // const toolsSkills = skills.filter(skill => skill.category === 'tools');
            //
            // populateSkills('frontend-skills', frontendSkills);
            // populateSkills('backend-skills', backendSkills);
            // populateSkills('tools-skills', toolsSkills);
        } catch (error) {
            console.error('Error fetching skills:', error);
        }
    };

    // Populate Skills
    const populateSkills = (containerId, skills) => {
        const container = document.getElementById(containerId);
        if (container && skills.length > 0) {
            container.innerHTML = '';

            skills.forEach(skill => {
                const skillItem = document.createElement('div');
                skillItem.className = 'skill-item';

                skillItem.innerHTML = `
                    <i class="${skill.icon}"></i>
                    <span>${skill.name}</span>
                `;

                container.appendChild(skillItem);
            });
        }
    };

    // Initialize data fetching
    // fetchProjects();
    // fetchSkills();

    // Header scroll effect
    window.addEventListener('scroll', function() {
        const header = document.querySelector('header');
        const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

        if (window.scrollY > 50) {
            header.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
        } else {
            header.style.boxShadow = 'none';
        }
    });
});
