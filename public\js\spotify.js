// Spotify Web Playback SDK Integration
class SpotifyPlayer {
    constructor() {
        this.player = null;
        this.deviceId = null;
        this.currentTrack = null;
        this.isPlaying = false;
        this.position = 0;
        this.duration = 0;
        this.accessToken = null;
        
        // DOM elements
        this.statusElement = document.getElementById('spotify-status');
        this.contentElement = document.getElementById('spotify-content');
        this.offlineElement = document.getElementById('spotify-offline');
        this.albumImage = document.getElementById('album-image');
        this.trackName = document.getElementById('track-name');
        this.artistName = document.getElementById('artist-name');
        this.albumName = document.getElementById('album-name');
        this.progressFill = document.getElementById('progress-fill');
        this.currentTime = document.getElementById('current-time');
        this.totalTime = document.getElementById('total-time');
        this.playPauseBtn = document.getElementById('play-pause-btn');
        this.prevBtn = document.getElementById('prev-btn');
        this.nextBtn = document.getElementById('next-btn');
        this.connectBtn = document.getElementById('connect-spotify-btn');
        
        this.init();
    }
    
    init() {
        // Check if Spotify Web Playback SDK is available
        if (window.Spotify) {
            this.initializePlayer();
        } else {
            // Load Spotify Web Playback SDK
            this.loadSpotifySDK();
        }
        
        this.setupEventListeners();
    }
    
    loadSpotifySDK() {
        const script = document.createElement('script');
        script.src = 'https://sdk.scdn.co/spotify-player.js';
        script.async = true;
        document.head.appendChild(script);
        
        window.onSpotifyWebPlaybackSDKReady = () => {
            this.initializePlayer();
        };
    }
    
    async initializePlayer() {
        this.showConnectingState();

        // Check for auth callback first
        const callbackToken = await SpotifyAuth.handleAuthCallback();
        if (callbackToken) {
            this.accessToken = callbackToken;
            console.log('✅ Spotify authentication successful!');
        } else {
            // Check for stored token
            this.accessToken = SpotifyAuth.getStoredToken();
            if (this.accessToken) {
                console.log('✅ Using stored Spotify token');
            }
        }

        if (this.accessToken) {
            try {
                await this.getCurrentPlayback();
                // Set up periodic refresh of playback state
                this.startPlaybackPolling();
            } catch (error) {
                console.error('❌ Error getting current playback:', error);
                if (error.message.includes('401')) {
                    // Token expired or invalid
                    SpotifyAuth.clearStoredToken();
                    this.accessToken = null;
                    this.showOfflineState();
                } else {
                    this.showOfflineState();
                }
            }
        } else {
            console.log('ℹ️ No Spotify token found, showing demo mode');
            // Show demo mode with option to connect
            setTimeout(() => {
                this.simulateCurrentTrack();
            }, 2000);
        }
    }
    
    setupEventListeners() {
        if (this.playPauseBtn) {
            this.playPauseBtn.addEventListener('click', () => {
                if (this.accessToken) {
                    this.realTogglePlayback();
                } else {
                    this.togglePlayback();
                }
            });
        }

        if (this.prevBtn) {
            this.prevBtn.addEventListener('click', () => {
                if (this.accessToken) {
                    this.realPreviousTrack();
                } else {
                    this.previousTrack();
                }
            });
        }

        if (this.nextBtn) {
            this.nextBtn.addEventListener('click', () => {
                if (this.accessToken) {
                    this.realNextTrack();
                } else {
                    this.nextTrack();
                }
            });
        }

        if (this.connectBtn) {
            this.connectBtn.addEventListener('click', () => {
                this.connectToSpotify();
            });
        }
    }
    
    showConnectingState() {
        this.statusElement.style.display = 'flex';
        this.contentElement.classList.add('hidden');
        this.offlineElement.classList.add('hidden');

        this.statusElement.innerHTML = `
            <i class="fab fa-spotify"></i>
            <span>Connecting to Spotify...</span>
        `;
    }

    showOfflineState() {
        this.statusElement.style.display = 'none';
        this.contentElement.classList.add('hidden');
        this.offlineElement.classList.remove('hidden');

        // Update the offline message based on authentication status
        const offlineContent = this.offlineElement;
        if (this.accessToken) {
            // User is authenticated but nothing is playing
            offlineContent.innerHTML = `
                <i class="fab fa-spotify"></i>
                <h3>Not Currently Playing</h3>
                <p>Start playing music on Spotify to see it here</p>
            `;
        } else {
            // User is not authenticated
            offlineContent.innerHTML = `
                <i class="fab fa-spotify"></i>
                <h3>Connect to Spotify</h3>
                <p>Connect your Spotify account to see what you're listening to</p>
                <button type="button" id="connect-spotify-btn" class="btn btn-primary spotify-connect-btn">
                    Connect to Spotify
                </button>
            `;

            // Re-attach event listener for the new button
            const newConnectBtn = document.getElementById('connect-spotify-btn');
            if (newConnectBtn) {
                newConnectBtn.addEventListener('click', () => {
                    this.connectToSpotify();
                });
            }
        }
    }

    showCurrentTrack(track) {
        this.statusElement.style.display = 'none';
        this.contentElement.classList.remove('hidden');
        this.offlineElement.classList.add('hidden');

        this.currentTrack = track;
        this.updateTrackInfo();
        this.startProgressUpdate();
    }
    
    updateTrackInfo() {
        if (!this.currentTrack) return;
        
        this.albumImage.src = this.currentTrack.album.images[0]?.url || '';
        this.trackName.textContent = this.currentTrack.name;
        this.artistName.textContent = this.currentTrack.artists.map(artist => artist.name).join(', ');
        this.albumName.textContent = this.currentTrack.album.name;
        this.duration = this.currentTrack.duration_ms;
        this.totalTime.textContent = this.formatTime(this.duration);
        
        this.updatePlayPauseButton();
    }
    
    updatePlayPauseButton() {
        const icon = this.playPauseBtn.querySelector('i');
        if (this.isPlaying) {
            icon.className = 'fas fa-pause';
        } else {
            icon.className = 'fas fa-play';
        }
    }
    
    updateProgress() {
        if (this.duration > 0) {
            const progressPercent = (this.position / this.duration) * 100;
            this.progressFill.style.width = `${progressPercent}%`;
            this.currentTime.textContent = this.formatTime(this.position);
        }
    }
    
    startProgressUpdate() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        this.progressInterval = setInterval(() => {
            if (this.isPlaying && this.position < this.duration) {
                this.position += 1000; // Increment by 1 second
                this.updateProgress();
            }
        }, 1000);
    }

    startPlaybackPolling() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
        }

        // Poll Spotify API every 30 seconds for updates
        this.pollingInterval = setInterval(() => {
            if (this.accessToken) {
                this.getCurrentPlayback();
            }
        }, 30000);
    }

    stopPolling() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }
    
    formatTime(ms) {
        const minutes = Math.floor(ms / 60000);
        const seconds = Math.floor((ms % 60000) / 1000);
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    
    togglePlayback() {
        this.isPlaying = !this.isPlaying;
        this.updatePlayPauseButton();
        
        // In a real implementation, you would call the Spotify API
        console.log(this.isPlaying ? 'Playing' : 'Paused');
    }
    
    previousTrack() {
        // In a real implementation, you would call the Spotify API
        console.log('Previous track');
        this.position = 0;
        this.updateProgress();
    }
    
    nextTrack() {
        // In a real implementation, you would call the Spotify API
        console.log('Next track');
        this.position = 0;
        this.updateProgress();
    }
    
    // Real Spotify API methods
    async getCurrentPlayback() {
        if (!this.accessToken) {
            this.showOfflineState();
            return;
        }

        try {
            const response = await fetch(`${SPOTIFY_CONFIG.API_BASE_URL}/me/player/currently-playing`, {
                headers: {
                    'Authorization': `Bearer ${this.accessToken}`
                }
            });

            if (response.status === 204) {
                // No content - nothing is playing
                console.log('ℹ️ No track currently playing');
                this.showOfflineState();
                return;
            }

            if (response.status === 401) {
                // Unauthorized - token expired
                console.log('❌ Spotify token expired');
                SpotifyAuth.clearStoredToken();
                this.accessToken = null;
                this.showOfflineState();
                return;
            }

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data && data.item) {
                console.log('🎵 Now playing:', data.item.name, 'by', data.item.artists[0].name);
                this.isPlaying = data.is_playing;
                this.position = data.progress_ms || 0;
                this.showCurrentTrack(data.item);
            } else {
                this.showOfflineState();
            }
        } catch (error) {
            console.error('❌ Error fetching current playback:', error);
            // Don't fallback to demo if we have a token - show offline instead
            if (this.accessToken) {
                this.showOfflineState();
            } else {
                this.simulateCurrentTrack();
            }
        }
    }

    async spotifyApiCall(endpoint, method = 'GET', body = null) {
        if (!this.accessToken) {
            throw new Error('No access token available');
        }

        const options = {
            method,
            headers: {
                'Authorization': `Bearer ${this.accessToken}`,
                'Content-Type': 'application/json'
            }
        };

        if (body) {
            options.body = JSON.stringify(body);
        }

        const response = await fetch(`${SPOTIFY_CONFIG.API_BASE_URL}${endpoint}`, options);

        if (!response.ok) {
            throw new Error(`Spotify API error: ${response.status}`);
        }

        return response.json();
    }

    async realTogglePlayback() {
        try {
            const endpoint = this.isPlaying ? '/me/player/pause' : '/me/player/play';
            await this.spotifyApiCall(endpoint, 'PUT');
            this.isPlaying = !this.isPlaying;
            this.updatePlayPauseButton();
        } catch (error) {
            console.error('Error toggling playback:', error);
            // Fallback to demo behavior
            this.togglePlayback();
        }
    }

    async realPreviousTrack() {
        try {
            await this.spotifyApiCall('/me/player/previous', 'POST');
            // Refresh current track info
            setTimeout(() => this.getCurrentPlayback(), 500);
        } catch (error) {
            console.error('Error skipping to previous track:', error);
            this.previousTrack();
        }
    }

    async realNextTrack() {
        try {
            await this.spotifyApiCall('/me/player/next', 'POST');
            // Refresh current track info
            setTimeout(() => this.getCurrentPlayback(), 500);
        } catch (error) {
            console.error('Error skipping to next track:', error);
            this.nextTrack();
        }
    }

    // Demo function to simulate a currently playing track
    simulateCurrentTrack() {
        const demoTrack = {
            name: "Blinding Lights",
            artists: [{ name: "The Weeknd" }],
            album: {
                name: "After Hours",
                images: [
                    {
                        url: "https://i.scdn.co/image/ab67616d0000b273c02f1aab5ffe0b5e3c8e3e5e"
                    }
                ]
            },
            duration_ms: 200040
        };

        this.isPlaying = true;
        this.position = 45000; // Start at 45 seconds
        this.showCurrentTrack(demoTrack);
    }

    // Add method to connect to Spotify
    connectToSpotify() {
        SpotifyAuth.redirectToSpotifyAuth();
    }
}

// Initialize Spotify player when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SpotifyPlayer();
});
