<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ouru.dev</title>
    <link rel="stylesheet" href="css/styles.css" />
    <!-- Favicon icon -->
    <link rel="icon" type="image/png" href="favicon/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="favicon/favicon.svg"/>
    <link rel="shortcut icon" href="favicon/favicon.ico"/>
    <link rel="apple-touch-icon" sizes="180x180" href="favicon/apple-touch-icon.png" />
    <link rel="manifest" href="favicon/site.webmanifest" />
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
    />
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Header -->
    <header>
      <div class="container">
        <nav>
          <div class="logo">
            <a href="/">ouru.dev</a>
          </div>
          <ul class="nav-links">
            <li><a href="#about">About</a></li>
            <li><a href="#projects">Projects</a></li>
            <li><a href="#skills">Skills</a></li>
            <li><a href="#contact">Contact</a></li>
            <li class="theme-toggle-container">
              <div class="theme-toggle">
                <input
                  type="checkbox"
                  id="theme-toggle-checkbox"
                  class="theme-toggle-checkbox"
                />
                <label for="theme-toggle-checkbox" class="theme-toggle-label">
                  <i class="fas fa-sun"></i>
                  <i class="fas fa-moon"></i>
                  <span class="theme-toggle-ball"></span>
                </label>
              </div>
            </li>
          </ul>
          <div class="hamburger">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </nav>
      </div>
    </header>

    <!-- Hero Section -->
    <section id="hero">
      <div class="container">
        <div class="hero-content">
          <h1>Hi, I'm <span class="highlight">Sheldon Ouru</span></h1>
          <h2>Full Stack Developer</h2>
          <p>
            I craft digital experiences that blend functionality with
            aesthetics.
          </p>
          <div class="hero-buttons">
            <a href="#projects" class="btn btn-primary">View Projects</a>
            <a href="#contact" class="btn btn-secondary">Contact Me</a>
          </div>
          <div class="social-links">
            <a href="https://github.com/" target="_blank"
              ><i class="fab fa-github"></i
            ></a>
            <a href="https://linkedin.com/" target="_blank"
              ><i class="fab fa-linkedin"></i
            ></a>
            <a href="https://x.com/ourusheldon?t=4gEzPIuF1M-ABH-UxuC6dw&s=09" target="_blank" rel="noopener"
              ><i class="fa-brands fa-x-twitter"></i
            ></a>
          </div>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section id="about">
      <div class="container">
        <h2 class="section-title">About Me</h2>
        <div class="about-content">
          <div class="about-text">
            <p>
              I'm a passionate full-stack developer with expertise in building
              modern web applications. With a strong foundation in both frontend
              and backend technologies, I create seamless, user-friendly
              experiences that solve real-world problems.
            </p>
            <p>
              I'm currently working on a variety of projects crafting and
              designing real world solutions. I'm constantly learning and
              adapting to new technologies.
            </p>
            <p>
              When I'm not coding, you can find me exploring new technologies,
              reviewing tech, interacting with the tech community or trying new
              tech opprtunities.
            </p>
          </div>
          <div class="about-image">
            <img src="images/profile.jpg" alt="Profile Picture" />
          </div>
        </div>
      </div>
    </section>

    <!-- Projects Section -->
    <section id="projects">
      <div class="container">
        <h2 class="section-title">Featured Projects</h2>
        <div class="projects-grid" id="projects-container">
          <!-- Projects will be loaded dynamically -->
          <div class="project-card">
            <div class="project-image">
              <img src="images/project1.jpg" alt="Project 1" />
            </div>
            <div class="project-content">
              <h3>PMS</h3>
              <p>
                A plartform empowering migrants achieve their dreams seamlessly.
                The plartform to seek career guidance and build performing
                startups.
              </p>
              <div class="project-tech">
                <span>React</span>
                <span>Node.js</span>
                <span>MongoDB</span>
                <span>Tailwind CSS</span>
              </div>
              <div class="project-links">
                <a href="#" target="_blank">View Project</a>
              </div>
            </div>
          </div>
          <div class="project-card">
            <div class="project-image">
              <img src="images/project2.jpg" alt="Project 2" />
            </div>
            <div class="project-content">
              <h3>ORON ORG</h3>
              <p>
                A dynamic web app for Ongata Rongai Organization For The Needy.
                A seamless fullstack platform to manage and collect donations
                for the valnurable.
              </p>
              <div class="project-tech">
                <span>HTML5</span>
                <span>CSS3</span>
                <span>Node.js</span>
                <span>Express.js</span>
                <span>MongoDB</span>
              </div>
              <div class="project-links">
                <a href="#" target="_blank"
                  ></i>View Project</a
                >
              </div>
            </div>
          </div>
          <div class="project-card">
            <div class="project-image">
              <img src="images/project3.jpg" alt="Project 3" />
            </div>
            <div class="project-content">
              <h3>M-pesa Sim Registration</h3>
              <p>
                Kenya's top mobile money platform. A seamless ecomerce site to order,
                buy and register M-pesa sim cards and have it delivered with ease.
              </p>
              <div class="project-tech">
                <span>React</span>
                <span>Javascript</span>
                <span>Tailwind CSS</span>
                <span>Daraja API</span>
              </div>
              <div class="project-links">
                <a href="https://mpesa-web-psi.vercel.app/" target="_blank"
                  ></i>View Project</a
                >
              </div>
            </div>
          </div>
        </div>
        <div class="view-more">
          <a href="/projects" class="btn btn-primary">View All Projects</a>
        </div>
      </div>
    </section>

    <!-- Skills Section -->
    <section id="skills">
      <div class="container">
        <h2 class="section-title">Skills & Technologies</h2>
        <div class="skills-container">
          <div class="skills-category">
            <h3>Frontend</h3>
            <div class="skills-grid" id="frontend-skills">
              <!-- Frontend skills will be loaded dynamically -->
              <div class="skill-item">
                <i class="fab fa-html5"></i>
                <span>HTML5</span>
              </div>
              <div class="skill-item">
                <i class="fab fa-css3-alt"></i>
                <span>CSS3</span>
              </div>
              <div class="skill-item">
                <i class="fab fa-js"></i>
                <span>JavaScript</span>
              </div>
              <div class="skill-item">
                <i class="fab fa-react"></i>
                <span>React</span>
              </div>
            </div>
          </div>
          <div class="skills-category">
            <h3>Backend</h3>
            <div class="skills-grid" id="backend-skills">
              <!-- Backend skills will be loaded dynamically -->
              <div class="skill-item">
                <i class="fab fa-node-js"></i>
                <span>Node.js</span>
              </div>
              <div class="skill-item">
                <i class="fab fa-node-js"></i>
                <span>Express</span>
              </div>
              <div class="skill-item">
                <i class="fas fa-database"></i>
                <span>MongoDB</span>
              </div>
              <div class="skill-item">
                <i class="fas fa-server"></i>
                <span>REST API</span>
              </div>
            </div>
          </div>
          <div class="skills-category">
            <h3>Tools & Others</h3>
            <div class="skills-grid" id="tools-skills">
              <!-- Tools skills will be loaded dynamically -->
              <div class="skill-item">
                <i class="fab fa-git-alt"></i>
                <span>Git</span>
              </div>
              <div class="skill-item">
                <i class="fab fa-github"></i>
                <span>GitHub</span>
              </div>
              <div class="skill-item">
                <i class="fab fa-docker"></i>
                <span>Docker</span>
              </div>
              <div class="skill-item">
                <i class="fas fa-terminal"></i>
                <span>Command Line</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact">
      <div class="container">
        <h2 class="section-title">Get In Touch</h2>
        <div class="contact-container">
          <div class="contact-info">
            <p>
              I'm currently open to new opportunities and collaborations. If you
              have a project that needs my expertise or just want to say hi,
              feel free to shoot me a DM!
            </p>
            <div class="contact-details">
              <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </div>
              <div class="contact-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>Nairobi, Kenya</span>
              </div>
            </div>
            <div class="social-links">
              <a href="https://github.com/" target="_blank"
                ><i class="fab fa-github"></i
              ></a>
              <a href="https://linkedin.com/" target="_blank"
                ><i class="fab fa-linkedin"></i
              ></a>
              <a href="https://x.com/ourusheldon?t=4gEzPIuF1M-ABH-UxuC6dw&s=09" target="_blank" rel="noopener"
                ><i class="fa-brands fa-x-twitter"></i
              ></a>
            </div>
          </div>
          <div class="contact-form">
            <form id="contact-form">
              <div class="form-group">
                <label for="name">Name</label>
                <input type="text" id="name" name="name" required />
              </div>
              <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required />
              </div>
              <div class="form-group">
                <label for="message">Message</label>
                <textarea
                  id="message"
                  name="message"
                  rows="5"
                  required
                ></textarea>
              </div>
              <button type="submit" class="btn btn-primary">
                Send Message
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer>
      <div class="container">
        <div class="footer-content">
          <p>&copy; 2025 ouru.dev. All rights reserved.</p>
          <div class="footer-links">
            <a href="#about">About</a>
            <a href="#projects">Projects</a>
            <a href="#skills">Skills</a>
            <a href="#contact">Contact</a>
          </div>
        </div>
      </div>
    </footer>

    <script src="js/api-config.js"></script>
    <!-- EmailJS SDK -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>
    <script type="text/javascript">
      (function() {
        // Initialize EmailJS with your public key
        emailjs.init({
          publicKey: "Wd_Oa-Wd_Oa-Wd_Oa", // Replace with your actual EmailJS public key
        });
      })();
    </script>
    <script src="js/main.js"></script>
  </body>
</html>
