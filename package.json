{"name": "portfolio-website", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "mongoose": "^8.13.2", "portfolio-website": "file:"}, "devDependencies": {"nodemon": "^3.1.9"}}