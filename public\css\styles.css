/* Base Styles */
:root {
  /* Light Theme (default) */
  --primary-color: #0070f3;
  --secondary-color: #6c757d;
  --dark-color: #1a1a1a;
  --light-color: #f8f9fa;
  --text-color: #333;
  --bg-color: #ffffff;
  --card-bg-color: #ffffff;
  --border-color: #e0e0e0;
  --success-color: #28a745;
  --error-color: #dc3545;
  --font-family: "Inter", sans-serif;
  --header-bg-color: rgba(255, 255, 255, 0.95);
  --footer-bg-color: #1a1a1a;
  --footer-text-color: #ffffff;
  --skill-item-bg: #f8f9fa;
  --placeholder-color: #999;
}

/* Dark Theme */
[data-theme="dark"] {
  --primary-color: #4da8ff;
  --secondary-color: #a0aec0;
  --dark-color: #f8f9fa;
  --light-color: #1a1a1a;
  --text-color: #f8f9fa;
  --bg-color: #121212;
  --card-bg-color: #1e1e1e;
  --border-color: #2d2d2d;
  --header-bg-color: rgba(18, 18, 18, 0.95);
  --footer-bg-color: #0a0a0a;
  --footer-text-color: #f8f9fa;
  --skill-item-bg: #1e1e1e;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--bg-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

a {
  text-decoration: none;
  color: var(--primary-color);
}

ul {
  list-style: none;
}

img {
  max-width: 100%;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-align: center;
  position: relative;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background-color: var(--primary-color);
}

.btn {
  display: inline-block;
  padding: 0.8rem 1.5rem;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Header */
header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: var(--header-bg-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 1rem 0;
  transition: background-color 0.3s ease;
}

nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo a {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--dark-color);
}

.nav-links {
  display: flex;
}

.nav-links li {
  margin-left: 2rem;
}

.nav-links a {
  color: var(--dark-color);
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-links a:hover {
  color: var(--primary-color);
}

.hamburger {
  display: none;
  cursor: pointer;
}

.hamburger span {
  display: block;
  width: 25px;
  height: 3px;
  background-color: var(--dark-color);
  margin: 5px 0;
  transition: all 0.3s ease;
}

/* Hero Section */
#hero {
  height: 100vh;
  display: flex;
  align-items: center;
  padding-top: 80px;
  background-color: var(--light-color);
}

.hero-content {
  max-width: 800px;
}

.hero-content h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
}

.hero-content h2 {
  font-size: 2rem;
  color: var(--secondary-color);
  margin-bottom: 1.5rem;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

.highlight {
  color: var(--primary-color);
}

.hero-buttons {
  margin-bottom: 2rem;
}

.hero-buttons .btn {
  margin-right: 1rem;
}

.social-links {
  display: flex;
  gap: 1.5rem;
}

.social-links a {
  font-size: 1.5rem;
  color: var(--dark-color);
  transition: color 0.3s ease;
}

.social-links a:hover {
  color: var(--primary-color);
}

/* About Section */
#about {
  padding: 6rem 0;
  background-color: var(--bg-color);
  transition: background-color 0.3s ease;
}

.about-content {
  display: flex;
  align-items: center;
  gap: 4rem;
}

.about-text {
  flex: 1;
}

.about-text p {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.about-image {
  flex: 1;
  text-align: center;
}

.about-image img {
  max-width: 80%;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

[data-theme="dark"] .about-image img {
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.05);
}

/* Projects Section */
#projects {
  padding: 6rem 0;
  background-color: var(--bg-color);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.project-card {
  background-color: var(--card-bg-color);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease,
    background-color 0.3s ease;
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.project-image {
  height: 200px;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.project-card:hover .project-image img {
  transform: scale(1.1);
}

.project-content {
  padding: 1.5rem;
}

.project-content h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.project-content p {
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.project-tech span {
  background-color: #f0f0f0;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

[data-theme="dark"] .project-tech span {
  background-color: #2d2d2d;
}

.project-links {
  display: flex;
  gap: 1.5rem;
}

.project-links a {
  color: var(--dark-color);
  font-weight: 500;
  transition: color 0.3s ease;
}

.project-links a:hover {
  color: var(--primary-color);
}

.view-more {
  text-align: center;
}

/* Skills Section */
#skills {
  padding: 6rem 0;
  background-color: var(--bg-color);
}

.skills-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
}

.skills-category h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  text-align: center;
  color: var(--primary-color);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.skill-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  background-color: var(--skill-item-bg);
  border-radius: 10px;
  transition: transform 0.3s ease, box-shadow 0.3s ease,
    background-color 0.3s ease;
}

.skill-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.skill-item i {
  font-size: 2.5rem;
  margin-bottom: 0.8rem;
  color: var(--primary-color);
}

.skill-item span {
  font-weight: 500;
}

/* Spotify Section */
#spotify {
  padding: 6rem 0;
  background-color: var(--light-color);
}

.spotify-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.spotify-card {
  background: linear-gradient(135deg, #1db954 0%, #1ed760 100%);
  border-radius: 20px;
  padding: 2rem;
  max-width: 500px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(29, 185, 84, 0.3);
  color: white;
  position: relative;
  overflow: hidden;
}

.spotify-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%
  );
  pointer-events: none;
}

.spotify-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 500;
}

.spotify-status i {
  font-size: 1.5rem;
}

.spotify-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.album-art {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.album-art img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sound-wave {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 16px;
}

.sound-wave .bar {
  width: 2px;
  background: #1db954;
  border-radius: 1px;
  animation: soundWave 1.5s ease-in-out infinite;
}

.sound-wave .bar:nth-child(1) {
  animation-delay: 0s;
}
.sound-wave .bar:nth-child(2) {
  animation-delay: 0.2s;
}
.sound-wave .bar:nth-child(3) {
  animation-delay: 0.4s;
}
.sound-wave .bar:nth-child(4) {
  animation-delay: 0.6s;
}

@keyframes soundWave {
  0%,
  100% {
    height: 4px;
  }
  50% {
    height: 16px;
  }
}

.track-info {
  text-align: center;
}

.track-info h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.track-info p {
  margin-bottom: 0.3rem;
  opacity: 0.9;
}

.track-info p:first-of-type {
  font-size: 1.1rem;
  font-weight: 500;
}

.progress-container {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: white;
  border-radius: 2px;
  transition: width 0.1s ease;
  width: 0%;
}

.time-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  opacity: 0.8;
}

.spotify-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.control-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.control-btn.play-btn {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  color: #1db954;
}

.control-btn.play-btn:hover {
  background: white;
  transform: scale(1.15);
}

.spotify-offline {
  text-align: center;
  padding: 2rem 0;
}

.spotify-offline i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.spotify-offline h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.spotify-offline p {
  opacity: 0.8;
}

/* Hidden state for Spotify elements */
.spotify-content.hidden,
.spotify-offline.hidden {
  display: none;
}

.spotify-connect-btn {
  margin-top: 1rem;
}

/* Contact Section */
#contact {
  padding: 6rem 0;
  background-color: var(--bg-color);
}

.contact-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 4rem;
}

.contact-info p {
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.contact-details {
  margin-bottom: 2rem;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.contact-item i {
  font-size: 1.2rem;
  margin-right: 1rem;
  color: var(--primary-color);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  font-family: var(--font-family);
  font-size: 1rem;
  background-color: var(--placeholder-color);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Footer */
footer {
  background-color: var(--footer-bg-color);
  color: var(--footer-text-color);
  padding: 2rem 0;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-links {
  display: flex;
  gap: 1.5rem;
}

.footer-links a {
  color: var(--footer-text-color);
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--primary-color);
}

/* Theme Toggle Switch */
.theme-toggle-container {
  display: flex;
  align-items: center;
}

.theme-toggle {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 30px;
}

.theme-toggle-checkbox {
  opacity: 0;
  width: 0;
  height: 0;
}

.theme-toggle-label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  border-radius: 30px;
  transition: 0.4s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5px;
}

.theme-toggle-label i {
  font-size: 14px;
}

.theme-toggle-label .fa-sun {
  color: #f39c12;
}

.theme-toggle-label .fa-moon {
  color: #f1c40f;
}

.theme-toggle-ball {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  border-radius: 50%;
  transition: 0.4s;
  z-index: 1;
}

.theme-toggle-checkbox:checked + .theme-toggle-label {
  background-color: #2c3e50;
}

.theme-toggle-checkbox:checked + .theme-toggle-label .theme-toggle-ball {
  transform: translateX(30px);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .about-content {
    flex-direction: column;
  }

  .about-image {
    order: -1;
    margin-bottom: 2rem;
  }

  .contact-container {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .spotify-card {
    padding: 1.5rem;
  }

  .album-art {
    width: 180px;
    height: 180px;
  }
}

@media (max-width: 768px) {
  .nav-links {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background-color: var(--bg-color);
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: left 0.3s ease, background-color 0.3s ease;
  }

  .theme-toggle-container {
    margin-top: 1.5rem;
  }

  .nav-links.active {
    left: 0;
  }

  .nav-links li {
    margin: 1.5rem 0;
  }

  .hamburger {
    display: block;
  }

  .hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .hamburger.active span:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .hero-content h2 {
    font-size: 1.5rem;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .skills-container {
    grid-template-columns: 1fr;
  }

  /* Footer links in list style for mobile */
  .footer-content {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .footer-links {
    display: grid;
    width: 100%;
    justify-content: center;
  }

  .footer-links a {
    display: block;
    position: relative;
  }
}

@media (max-width: 576px) {
  .section-title {
    font-size: 2rem;
  }

  .hero-buttons .btn {
    display: block;
    width: 100%;
    margin-bottom: 1rem;
  }

  .skills-grid {
    grid-template-columns: 1fr;
  }

  .spotify-card {
    padding: 1rem;
    margin: 0 1rem;
  }

  .album-art {
    width: 150px;
    height: 150px;
  }

  .track-info h3 {
    font-size: 1.2rem;
  }

  .spotify-controls {
    gap: 0.5rem;
  }

  .control-btn {
    width: 45px;
    height: 45px;
  }

  .control-btn.play-btn {
    width: 55px;
    height: 55px;
  }
}
