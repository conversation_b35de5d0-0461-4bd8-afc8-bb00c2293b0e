/* Update Notification Styles */
.update-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: var(--card-bg-color);
  border-radius: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  padding: 20px;
  max-width: 350px;
  z-index: 9999;
  transform: translateY(100px);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
  border-left: 4px solid var(--primary-color);
}

.update-notification.show {
  transform: translateY(0);
  opacity: 1;
}

.update-notification-content {
  color: var(--text-color);
}

.update-notification-content p {
  margin-bottom: 15px;
}

.update-notification-content .update-notes {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 8px 12px;
  border-radius: 5px;
  font-style: italic;
  margin-bottom: 15px;
  font-size: 14px;
}

[data-theme="dark"] .update-notification-content .update-notes {
  background-color: rgba(255, 255, 255, 0.1);
}

.update-notification-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.update-notification-buttons button {
  padding: 8px 15px;
  font-size: 14px;
}

/* Mobile Responsive */
@media (max-width: 576px) {
  .update-notification {
    left: 20px;
    right: 20px;
    max-width: none;
  }

  .update-notification-buttons {
    flex-direction: column;
  }

  .update-notification-buttons button {
    width: 100%;
    margin-bottom: 5px;
  }
}
