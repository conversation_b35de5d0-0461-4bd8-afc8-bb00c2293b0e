const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://ouru-69rui7evi-ourus-projects.vercel.app', 'https://ourudev-omega.vercel.app', 'https://ouru.dev']
    : 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Determine the correct public directory path based on environment
let publicPath = path.join(__dirname, 'public');

// Check if we're running on Render
if (process.env.RENDER && process.env.NODE_ENV === 'production') {
  // On Render, the app is deployed to /opt/render/project/src/
  publicPath = path.join(__dirname, 'public');
  console.log('Running on Render, using path:', publicPath);

  // Create public directory if it doesn't exist
  if (!fs.existsSync(publicPath)) {
    console.log('Public directory does not exist, creating it...');
    fs.mkdirSync(publicPath, { recursive: true });
  }

  // Create a basic index.html file if it doesn't exist
  const indexPath = path.join(publicPath, 'index.html');
  if (!fs.existsSync(indexPath)) {
    console.log('index.html does not exist, creating a basic one...');
    const basicHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ouru.dev</title>
</head>
<body>
  <h1>ouru.dev API Server</h1>
  <p>The API is running. This is a placeholder page.</p>
</body>
</html>`;
    fs.writeFileSync(indexPath, basicHtml);
  }
}

// Serve static files
app.use(express.static(publicPath));

// For production, also serve the Vercel deployment files if they exist
if (process.env.NODE_ENV === 'production' && !process.env.RENDER) {
  app.use(express.static(path.join(__dirname, 'vercel-deploy', 'public')));
}

// MongoDB Connection
const connectDB = require('./src/config/db');
connectDB();

// Routes
app.get('/api', (req, res) => {
  res.json({ message: 'ouru.dev API is running' });
});

// Serve index.html for the root route
app.get('/', (req, res) => {
  res.sendFile(path.join(publicPath, 'index.html'));
});

// API Routes
const projectRoutes = require('./src/routes/projectRoutes');
const skillRoutes = require('./src/routes/skillRoutes');
const contactRoutes = require('./src/routes/contactRoutes');

app.use('/api/projects', projectRoutes);
app.use('/api/skills', skillRoutes);
app.use('/api/contact', contactRoutes);

// Catch-all route for client-side routing (SPA)
app.use((req, res) => {
  res.sendFile(path.join(publicPath, 'index.html'));
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

