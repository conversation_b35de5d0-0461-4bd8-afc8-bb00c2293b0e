// Spotify Configuration
const SPOTIFY_CONFIG = {
    // Replace with your actual Spotify app credentials
    CLIENT_ID: '104abf9caaa841e6829f763a4867c324',
    
    // Redirect URI (must be registered in your Spotify app settings)
    REDIRECT_URI: window.location.origin,
    
    // Scopes required for the application
    SCOPES: [
        'streaming',
        'user-read-email',
        'user-read-private',
        'user-read-playback-state',
        'user-modify-playback-state',
        'user-read-currently-playing'
    ].join(' '),
    
    // Spotify Web API endpoints
    API_BASE_URL: 'https://api.spotify.com/v1',
    
    // Token storage key
    TOKEN_STORAGE_KEY: 'spotify_access_token',
    
    // Token expiry storage key
    TOKEN_EXPIRY_KEY: 'spotify_token_expiry'
};

// Spotify Authentication Helper
class SpotifyAuth {
    static generateRandomString(length) {
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        const values = crypto.getRandomValues(new Uint8Array(length));
        return values.reduce((acc, x) => acc + possible[x % possible.length], "");
    }
    
    static async sha256(plain) {
        const encoder = new TextEncoder();
        const data = encoder.encode(plain);
        return window.crypto.subtle.digest('SHA-256', data);
    }
    
    static base64encode(input) {
        return btoa(String.fromCharCode(...new Uint8Array(input)))
            .replace(/=/g, '')
            .replace(/\+/g, '-')
            .replace(/\//g, '_');
    }
    
    static async generateCodeChallenge(codeVerifier) {
        const hashed = await this.sha256(codeVerifier);
        return this.base64encode(hashed);
    }
    
    static async redirectToSpotifyAuth() {
        const codeVerifier = this.generateRandomString(64);
        const codeChallenge = await this.generateCodeChallenge(codeVerifier);
        
        // Store code verifier for later use
        localStorage.setItem('code_verifier', codeVerifier);
        
        const authUrl = new URL('https://accounts.spotify.com/authorize');
        const params = {
            response_type: 'code',
            client_id: SPOTIFY_CONFIG.CLIENT_ID,
            scope: SPOTIFY_CONFIG.SCOPES,
            code_challenge_method: 'S256',
            code_challenge: codeChallenge,
            redirect_uri: SPOTIFY_CONFIG.REDIRECT_URI,
        };
        
        authUrl.search = new URLSearchParams(params).toString();
        window.location.href = authUrl.toString();
    }
    
    static async exchangeCodeForToken(code) {
        const codeVerifier = localStorage.getItem('code_verifier');
        
        const response = await fetch('https://accounts.spotify.com/api/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                client_id: SPOTIFY_CONFIG.CLIENT_ID,
                grant_type: 'authorization_code',
                code: code,
                redirect_uri: SPOTIFY_CONFIG.REDIRECT_URI,
                code_verifier: codeVerifier,
            }),
        });
        
        if (!response.ok) {
            throw new Error('Failed to exchange code for token');
        }
        
        const data = await response.json();
        
        // Store token and expiry time
        const expiryTime = Date.now() + (data.expires_in * 1000);
        localStorage.setItem(SPOTIFY_CONFIG.TOKEN_STORAGE_KEY, data.access_token);
        localStorage.setItem(SPOTIFY_CONFIG.TOKEN_EXPIRY_KEY, expiryTime.toString());
        
        // Clean up
        localStorage.removeItem('code_verifier');
        
        return data.access_token;
    }
    
    static getStoredToken() {
        const token = localStorage.getItem(SPOTIFY_CONFIG.TOKEN_STORAGE_KEY);
        const expiry = localStorage.getItem(SPOTIFY_CONFIG.TOKEN_EXPIRY_KEY);
        
        if (!token || !expiry) {
            return null;
        }
        
        // Check if token is expired
        if (Date.now() > parseInt(expiry)) {
            this.clearStoredToken();
            return null;
        }
        
        return token;
    }
    
    static clearStoredToken() {
        localStorage.removeItem(SPOTIFY_CONFIG.TOKEN_STORAGE_KEY);
        localStorage.removeItem(SPOTIFY_CONFIG.TOKEN_EXPIRY_KEY);
    }
    
    static async handleAuthCallback() {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const error = urlParams.get('error');
        
        if (error) {
            console.error('Spotify auth error:', error);
            return null;
        }
        
        if (code) {
            try {
                const token = await this.exchangeCodeForToken(code);
                // Clean up URL
                window.history.replaceState({}, document.title, window.location.pathname);
                return token;
            } catch (error) {
                console.error('Error exchanging code for token:', error);
                return null;
            }
        }
        
        return null;
    }
}

// Export for use in other scripts
window.SPOTIFY_CONFIG = SPOTIFY_CONFIG;
window.SpotifyAuth = SpotifyAuth;
