# MongoDB Atlas Setup Guide

This guide will help you set up MongoDB Atlas for your Render deployment.

## 1. Create a MongoDB Atlas Account

If you don't already have a MongoDB Atlas account:

1. Go to [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. Sign up for a free account

## 2. Create a Cluster

1. Click "Build a Database"
2. Choose the free tier option (M0)
3. Select your preferred cloud provider and region
4. Click "Create Cluster"

## 3. Set Up Database Access

1. In the left sidebar, click "Database Access"
2. Click "Add New Database User"
3. Choose "Password" authentication method
4. Enter a username and password
   - Username: `ourusheldon` (or your preferred username)
   - Password: Create a strong password
5. Set database user privileges to "Atlas admin" for simplicity
6. Click "Add User"

## 4. Configure Network Access

This is a critical step for Render deployments:

1. In the left sidebar, click "Network Access"
2. Click "Add IP Address"
3. Click "Allow Access from Anywhere" (this adds 0.0.0.0/0)
4. Click "Confirm"

## 5. Get Your Connection String

1. Go back to your cluster and click "Connect"
2. <PERSON><PERSON> "Connect your application"
3. Select "Node.js" as your driver and the appropriate version
4. Copy the connection string
5. Replace `<password>` with your actual password
6. Replace `<dbname>` with your database name (e.g., `portfolio`)

## 6. Update Your Render Environment Variables

1. Go to your Render dashboard
2. Select your web service
3. Go to "Environment" tab
4. Add or update the `MONGODB_URI` environment variable with your connection string
5. Click "Save Changes"

## 7. Test the Connection

1. Run the test script in your local environment:
   ```
   node test-mongodb-connection.js
   ```
2. If successful, deploy to Render

## Troubleshooting

If you encounter connection issues:

1. Verify your IP whitelist includes 0.0.0.0/0
2. Check your username and password
3. Make sure your MongoDB Atlas cluster is running
4. Try creating a new database user
5. Check the MongoDB Atlas status page for any outages

## Security Note

Allowing access from anywhere (0.0.0.0/0) is convenient for development but not ideal for production. For a more secure setup, you should:

1. Identify the specific IP ranges used by Render
2. Add only those IP ranges to your whitelist
3. Remove the 0.0.0.0/0 entry once your application is working
