/**
 * Vercel Deployment Helper Script
 * 
 * This script prepares the project for deployment to Vercel by creating a simplified structure.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Vercel Deployment Preparation Script');
console.log('--------------------------------------');

// Create a deployment directory
const deployDir = path.join(__dirname, 'vercel-deploy');
if (!fs.existsSync(deployDir)) {
  fs.mkdirSync(deployDir);
  console.log(`✅ Created deployment directory: ${deployDir}`);
}

// Copy public directory to deployment directory
const publicDir = path.join(__dirname, 'public');
const deployPublicDir = path.join(deployDir, 'public');
if (!fs.existsSync(deployPublicDir)) {
  fs.mkdirSync(deployPublicDir);
  console.log(`✅ Created public directory in deployment: ${deployPublicDir}`);
}

// Function to copy directory recursively
function copyDir(src, dest) {
  const entries = fs.readdirSync(src, { withFileTypes: true });
  
  entries.forEach(entry => {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      if (!fs.existsSync(destPath)) {
        fs.mkdirSync(destPath);
      }
      copyDir(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  });
}

// Copy public directory contents
copyDir(publicDir, deployPublicDir);
console.log('✅ Copied public directory contents to deployment directory');

// Create a simplified vercel.json
const vercelJsonContent = `{
  "version": 2,
  "builds": [
    { "src": "public/**", "use": "@vercel/static" }
  ],
  "routes": [
    { "src": "/(.*)", "dest": "/public/$1" },
    { "handle": "filesystem" },
    { "src": "/.*", "dest": "/public/index.html" }
  ]
}`;

fs.writeFileSync(path.join(deployDir, 'vercel.json'), vercelJsonContent);
console.log('✅ Created vercel.json in deployment directory');

console.log('\n📋 Deployment Instructions:');
console.log('1. Navigate to the deployment directory:');
console.log(`   cd ${deployDir}`);
console.log('\n2. Deploy to Vercel:');
console.log('   vercel');
console.log('\n3. Follow the prompts to configure your deployment.');
console.log('\n4. Deploy to production:');
console.log('   vercel --prod');

console.log('\n✅ Vercel deployment preparation complete!');
