// This file is for testing the update notification system
// It simulates an update by changing the version in version.json

function simulateUpdate() {
  // Create a new version object with an incremented version number
  const newVersion = {
    version: "1.0.1",
    releaseDate: new Date().toISOString().split('T')[0],
    notes: "This is a simulated update for testing purposes."
  };
  
  // Display an alert to show what's happening
  alert("Simulating an app update...\n\nThis would normally happen on your server, but for testing purposes, we're simulating it in the browser.\n\nAfter clicking OK, you should see an update notification appear.");
  
  // In a real app, you would update the version.json file on the server
  // For testing, we'll override the fetch function to return our new version
  const originalFetch = window.fetch;
  
  window.fetch = function(url, options) {
    if (url.includes('version.json')) {
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve(newVersion)
      });
    }
    
    return originalFetch(url, options);
  };
  
  // Trigger the update check
  APP_UPDATER.checkForUpdates();
  
  // Restore the original fetch after 10 seconds
  setTimeout(() => {
    window.fetch = originalFetch;
    console.log('Restored original fetch function');
  }, 10000);
}

// Add a hidden button to the page that can be triggered from the console
document.addEventListener('DOMContentLoaded', function() {
  const simulateBtn = document.createElement('button');
  simulateBtn.id = 'simulate-update-btn';
  simulateBtn.textContent = 'Simulate Update';
  simulateBtn.style.position = 'fixed';
  simulateBtn.style.bottom = '10px';
  simulateBtn.style.right = '10px';
  simulateBtn.style.zIndex = '9999';
  simulateBtn.style.display = 'none'; // Hidden by default
  simulateBtn.onclick = simulateUpdate;
  
  document.body.appendChild(simulateBtn);
  
  console.log('To simulate an update, run: document.getElementById("simulate-update-btn").click()');
});
