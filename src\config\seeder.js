const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Project = require('../models/Project');
const Skill = require('../models/Skill');

// Load env vars
dotenv.config();

// Connect to DB
mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://ourusheldon:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0');

// Sample Projects Data
const projectsData = [
  {
    title: 'E-Commerce Platform',
    description: 'A full-featured e-commerce platform with product management, cart functionality, and payment processing.',
    image: '/images/project1.jpg',
    technologies: ['React', 'Node.js', 'MongoDB', 'Express'],
    githubLink: 'https://github.com/username/ecommerce',
    liveLink: 'https://ecommerce-demo.com',
    featured: true
  },
  {
    title: 'Task Management App',
    description: 'A collaborative task management application with real-time updates and team collaboration features.',
    image: '/images/project2.jpg',
    technologies: ['Vue.js', 'Express', 'Socket.io', 'MongoDB'],
    githubLink: 'https://github.com/username/taskmanager',
    liveLink: 'https://taskmanager-demo.com',
    featured: true
  },
  {
    title: 'Weather Dashboard',
    description: 'A weather application that provides real-time weather data and forecasts for locations worldwide.',
    image: '/images/project3.jpg',
    technologies: ['JavaScript', 'Weather API', 'CSS3', 'HTML5'],
    githubLink: 'https://github.com/username/weather-app',
    liveLink: 'https://weather-demo.com',
    featured: true
  }
];

// Sample Skills Data
const skillsData = [
  {
    name: 'HTML5',
    icon: 'fab fa-html5',
    category: 'frontend'
  },
  {
    name: 'CSS3',
    icon: 'fab fa-css3-alt',
    category: 'frontend'
  },
  {
    name: 'JavaScript',
    icon: 'fab fa-js',
    category: 'frontend'
  },
  {
    name: 'React',
    icon: 'fab fa-react',
    category: 'frontend'
  },
  {
    name: 'Node.js',
    icon: 'fab fa-node-js',
    category: 'backend'
  },
  {
    name: 'Express',
    icon: 'fab fa-node-js',
    category: 'backend'
  },
  {
    name: 'MongoDB',
    icon: 'fas fa-database',
    category: 'backend'
  },
  {
    name: 'REST API',
    icon: 'fas fa-server',
    category: 'backend'
  },
  {
    name: 'Git',
    icon: 'fab fa-git-alt',
    category: 'tools'
  },
  {
    name: 'GitHub',
    icon: 'fab fa-github',
    category: 'tools'
  },
  {
    name: 'Docker',
    icon: 'fab fa-docker',
    category: 'tools'
  },
  {
    name: 'Command Line',
    icon: 'fas fa-terminal',
    category: 'tools'
  }
];

// Import data into DB
const importData = async () => {
  try {
    await Project.deleteMany();
    await Skill.deleteMany();

    await Project.insertMany(projectsData);
    await Skill.insertMany(skillsData);

    console.log('Data imported successfully');
    process.exit();
  } catch (error) {
    console.error(error);
    process.exit(1);
  }
};

// Delete data from DB
const deleteData = async () => {
  try {
    await Project.deleteMany();
    await Skill.deleteMany();

    console.log('Data destroyed successfully');
    process.exit();
  } catch (error) {
    console.error(error);
    process.exit(1);
  }
};

// Run command: node seeder.js -i (to import) or node seeder.js -d (to delete)
if (process.argv[2] === '-i') {
  importData();
} else if (process.argv[2] === '-d') {
  deleteData();
} else {
  console.log('Please use -i to import or -d to delete data');
  process.exit();
}
