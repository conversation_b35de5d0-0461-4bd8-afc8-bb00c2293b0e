// Spotify Integration Test Utilities
// This file provides utilities to test and debug the Spotify integration

class SpotifyTester {
    static async testConfiguration() {
        console.log('🧪 Testing Spotify Configuration...');
        
        // Check if configuration is loaded
        if (typeof SPOTIFY_CONFIG === 'undefined') {
            console.error('❌ SPOTIFY_CONFIG not found. Make sure spotify-config.js is loaded.');
            return false;
        }
        
        console.log('✅ Configuration loaded');
        console.log('📋 Client ID:', SPOTIFY_CONFIG.CLIENT_ID);
        console.log('📋 Redirect URI:', SPOTIFY_CONFIG.REDIRECT_URI);
        console.log('📋 Dev Mode:', SPOTIFY_CONFIG.DEV_MODE);
        
        // Check if Client ID is set
        if (SPOTIFY_CONFIG.CLIENT_ID === 'your_spotify_client_id_here') {
            console.warn('⚠️ Client ID not updated. Please set your actual Spotify Client ID.');
            return false;
        }
        
        console.log('✅ Client ID is configured');
        
        // Test if we can reach Spotify's auth endpoint
        try {
            const authUrl = 'https://accounts.spotify.com/authorize';
            const response = await fetch(authUrl, { method: 'HEAD', mode: 'no-cors' });
            console.log('✅ Can reach Spotify auth endpoint');
        } catch (error) {
            console.warn('⚠️ Cannot verify Spotify auth endpoint connectivity');
        }
        
        return true;
    }
    
    static async testStoredToken() {
        console.log('🧪 Testing Stored Token...');
        
        const token = SpotifyAuth.getStoredToken();
        if (!token) {
            console.log('ℹ️ No stored token found');
            return false;
        }
        
        console.log('✅ Stored token found');
        
        // Test token validity
        try {
            const response = await fetch('https://api.spotify.com/v1/me', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (response.ok) {
                const user = await response.json();
                console.log('✅ Token is valid');
                console.log('👤 Authenticated as:', user.display_name || user.id);
                return true;
            } else {
                console.log('❌ Token is invalid or expired');
                SpotifyAuth.clearStoredToken();
                return false;
            }
        } catch (error) {
            console.error('❌ Error testing token:', error);
            return false;
        }
    }
    
    static async testCurrentPlayback() {
        console.log('🧪 Testing Current Playback...');
        
        const token = SpotifyAuth.getStoredToken();
        if (!token) {
            console.log('❌ No token available for testing');
            return false;
        }
        
        try {
            const response = await fetch('https://api.spotify.com/v1/me/player/currently-playing', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (response.status === 204) {
                console.log('ℹ️ No track currently playing');
                return true;
            }
            
            if (response.ok) {
                const data = await response.json();
                if (data && data.item) {
                    console.log('✅ Current playback retrieved');
                    console.log('🎵 Track:', data.item.name);
                    console.log('👨‍🎤 Artist:', data.item.artists[0].name);
                    console.log('▶️ Playing:', data.is_playing);
                    return true;
                } else {
                    console.log('ℹ️ No playback data available');
                    return true;
                }
            } else {
                console.error('❌ Error fetching playback:', response.status, response.statusText);
                return false;
            }
        } catch (error) {
            console.error('❌ Error testing playback:', error);
            return false;
        }
    }
    
    static async runFullTest() {
        console.log('🚀 Running Full Spotify Integration Test...');
        console.log('=====================================');
        
        const configTest = await this.testConfiguration();
        const tokenTest = await this.testStoredToken();
        const playbackTest = tokenTest ? await this.testCurrentPlayback() : false;
        
        console.log('=====================================');
        console.log('📊 Test Results:');
        console.log('Configuration:', configTest ? '✅ PASS' : '❌ FAIL');
        console.log('Token:', tokenTest ? '✅ PASS' : '❌ FAIL');
        console.log('Playback:', playbackTest ? '✅ PASS' : '❌ FAIL');
        
        if (configTest && tokenTest && playbackTest) {
            console.log('🎉 All tests passed! Spotify integration is working correctly.');
        } else if (configTest && !tokenTest) {
            console.log('🔗 Configuration is correct. Connect to Spotify to complete setup.');
        } else {
            console.log('🔧 Some tests failed. Check the configuration and try again.');
        }
        
        return { configTest, tokenTest, playbackTest };
    }
    
    static clearAllData() {
        console.log('🧹 Clearing all Spotify data...');
        SpotifyAuth.clearStoredToken();
        localStorage.removeItem('code_verifier');
        console.log('✅ All Spotify data cleared');
    }
    
    static showDebugInfo() {
        console.log('🔍 Spotify Debug Information:');
        console.log('Current URL:', window.location.href);
        console.log('Origin:', window.location.origin);
        console.log('Stored Token:', !!SpotifyAuth.getStoredToken());
        console.log('Token Expiry:', localStorage.getItem(SPOTIFY_CONFIG.TOKEN_EXPIRY_KEY));
        console.log('Code Verifier:', !!localStorage.getItem('code_verifier'));
    }
}

// Make available globally for console testing
window.SpotifyTester = SpotifyTester;

// Auto-run basic test in development mode
if (SPOTIFY_CONFIG && SPOTIFY_CONFIG.DEV_MODE) {
    console.log('🔧 Development mode detected - Spotify test utilities available');
    console.log('💡 Run SpotifyTester.runFullTest() to test the integration');
}
