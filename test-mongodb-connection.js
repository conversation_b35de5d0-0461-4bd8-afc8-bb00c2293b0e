/**
 * MongoDB Connection Test Script
 * 
 * This script tests the connection to MongoDB Atlas to help troubleshoot connection issues.
 * Run with: node test-mongodb-connection.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Get the MongoDB URI from environment variables
const mongoURI = process.env.MONGODB_URI;

if (!mongoURI) {
  console.error('MongoDB URI is not defined. Please set the MONGODB_URI environment variable.');
  process.exit(1);
}

// Hide credentials in logs
const uriForLogs = mongoURI.replace(/mongodb\+srv:\/\/([^:]+):[^@]+@/, 'mongodb+srv://$1:****@');
console.log(`Attempting to connect to MongoDB: ${uriForLogs}`);

// Connection options
const options = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  maxPoolSize: 10,
  retryWrites: true,
  retryReads: true
};

// Connect to MongoDB
mongoose.connect(mongoURI, options)
  .then(() => {
    console.log('✅ MongoDB connection successful!');
    console.log(`Connected to: ${mongoose.connection.host}`);
    console.log(`Database name: ${mongoose.connection.name}`);
    
    // List collections to verify access
    return mongoose.connection.db.listCollections().toArray();
  })
  .then(collections => {
    console.log('\nCollections in database:');
    if (collections.length === 0) {
      console.log('No collections found. Database may be empty.');
    } else {
      collections.forEach(collection => {
        console.log(`- ${collection.name}`);
      });
    }
    
    // Close the connection
    return mongoose.connection.close();
  })
  .then(() => {
    console.log('\nConnection closed successfully.');
    process.exit(0);
  })
  .catch(err => {
    console.error('❌ MongoDB connection error:');
    console.error(err);
    
    // Provide troubleshooting guidance based on error type
    if (err.name === 'MongoServerSelectionError') {
      console.log('\nTroubleshooting tips for server selection errors:');
      console.log('1. Check if your MongoDB Atlas cluster is running');
      console.log('2. Verify that your IP whitelist includes 0.0.0.0/0 for Render access');
      console.log('3. Check if there are any MongoDB Atlas maintenance events');
    } else if (err.message.includes('bad auth')) {
      console.log('\nTroubleshooting tips for authentication errors:');
      console.log('1. Verify your username and password in the connection string');
      console.log('2. Check if your MongoDB Atlas user has the correct permissions');
      console.log('3. Try creating a new database user in MongoDB Atlas');
      console.log('4. Make sure you\'re connecting to the correct database');
    } else if (err.name === 'MongoNetworkError') {
      console.log('\nTroubleshooting tips for network errors:');
      console.log('1. Check your network connectivity');
      console.log('2. Verify that your firewall allows outbound connections to MongoDB Atlas');
      console.log('3. Try connecting from a different network');
    }
    
    process.exit(1);
  });

console.log('Connecting to MongoDB...');
