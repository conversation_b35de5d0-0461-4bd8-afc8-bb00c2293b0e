const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    // In production, we must use MongoDB Atlas or another cloud MongoDB provider
    // The MONGODB_URI should be set in the Render environment variables
    let mongoURI = process.env.MONGODB_URI;

    // Log connection attempt (without exposing full credentials)
    if (mongoURI) {
      const uriParts = mongoURI.split('@');
      if (uriParts.length > 1) {
        console.log(`Attempting to connect to MongoDB at ${uriParts[1]}`);
      } else {
        console.log('Attempting to connect to MongoDB with URI (credentials hidden)');
      }
    } else {
      console.log('MongoDB URI is undefined');
    }

    if (!mongoURI) {
      console.error('MongoDB URI is not defined. Please set the MONGODB_URI environment variable.');
      if (process.env.NODE_ENV === 'production') {
        // In production, use a default connection string for testing
        // This is not ideal but prevents the app from crashing
        console.log('Using fallback connection for testing purposes only');
        // Create a fallback connection string for testing only
        mongoURI = 'mongodb+srv://ourusheldon:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
      } else {
        // In development, exit the process
        process.exit(1);
      }
    }

    // Add retry logic for cloud database connections
    const options = {
      // Keep the other valid options
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      maxPoolSize: 10,
      minPoolSize: 5,
      retryWrites: true,
      retryReads: true
    };

    if (mongoURI) {
      const conn = await mongoose.connect(mongoURI, options);
      console.log(`MongoDB Connected: ${conn.connection.host}`);
    } else {
      console.log('Skipping MongoDB connection due to missing URI');
    }
  } catch (error) {
    console.error(`MongoDB Connection Error: ${error.message}`);

    // Provide more detailed error information
    if (error.name === 'MongoServerSelectionError') {
      console.error('Failed to select a MongoDB server. Check network connectivity and MongoDB Atlas status.');
    } else if (error.name === 'MongoNetworkError') {
      console.error('Network error connecting to MongoDB. Check your network and firewall settings.');
    } else if (error.message.includes('bad auth')) {
      console.error('Authentication failed. Check your MongoDB username and password.');
      console.error('Make sure your MongoDB Atlas IP whitelist includes 0.0.0.0/0 for Render access.');

      // Try to connect with a modified URI that explicitly sets authSource
      try {
        console.log('Attempting connection with explicit authSource...');
        let modifiedURI = process.env.MONGODB_URI;
        if (modifiedURI && !modifiedURI.includes('authSource=')) {
          modifiedURI += '&authSource=admin';
          const options = {
            useNewUrlParser: true,
            useUnifiedTopology: true,
            serverSelectionTimeoutMS: 30000
          };
          console.log('Connecting with modified URI...');
          const conn = await mongoose.connect(modifiedURI, options);
          console.log(`MongoDB Connected with modified URI: ${conn.connection.host}`);
          return; // Successfully connected
        }
      } catch (retryError) {
        console.error(`Retry connection failed: ${retryError.message}`);
      }
    }

    // Don't exit the process in production, just log the error
    if (process.env.NODE_ENV !== 'production') {
      process.exit(1);
    }
  }
};

module.exports = connectDB;

