# Spotify Integration Setup

This portfolio includes a "Now Playing" section that displays your currently playing Spotify track. The integration works in two modes:

## Demo Mode (Default)

By default, the Spotify section shows a demo track to demonstrate the functionality. This works without any setup.

## Live Spotify Integration

To connect to your actual Spotify account and show real-time playback information, follow these steps:

### 1. Create a Spotify App

1. Go to the [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Log in with your Spotify account
3. Click "Create an App"
4. Fill in the app details:
   - **App Name**: Your Portfolio Spotify Integration
   - **App Description**: Displays currently playing track on portfolio website
5. Accept the terms and create the app

### 2. Configure Your App

1. In your app dashboard, click "Settings"
2. Add your website URL to "Redirect URIs":
   - For local development: `http://localhost:3000` (or your local port)
   - For production: `https://yourdomain.com`
3. Save the settings

### 3. Update Configuration

✅ **Already Done!** Your Client ID is already configured: `104abf9caaa841e6829f763a4867c324`

Make sure your Spotify app settings include these redirect URIs:

- For local development: `http://localhost:3000` (or your local port)
- For production: `https://yourdomain.com`

The configuration in `public/js/spotify-config.js` should look like this:

```javascript
const SPOTIFY_CONFIG = {
  CLIENT_ID: "104abf9caaa841e6829f763a4867c324", // ✅ Already set
  REDIRECT_URI: window.location.origin, // ✅ Auto-detects your domain
  // ... rest of config
};
```

### 4. Test the Integration

1. Open your portfolio website
2. Navigate to the "Now Playing" section
3. Click "Connect to Spotify"
4. Authorize the app in the Spotify popup
5. Start playing music in Spotify
6. The section should now show your currently playing track

### 5. Troubleshooting with the Test Utility

A test utility is included to help diagnose any issues with the Spotify integration. Open your browser's developer console (F12) and run:

```javascript
SpotifyTester.runFullTest();
```

This will check:

- If your configuration is correct
- If you have a valid token
- If playback data can be retrieved

You can also run these individual tests:

```javascript
// Test just the configuration
SpotifyTester.testConfiguration();

// Test if your token is valid
SpotifyTester.testStoredToken();

// Test if playback data can be retrieved
SpotifyTester.testCurrentPlayback();

// Clear all Spotify data (for troubleshooting)
SpotifyTester.clearAllData();
```

## Features

### What Works

- ✅ Display currently playing track information
- ✅ Show album artwork
- ✅ Display track progress
- ✅ Play/pause control
- ✅ Previous/next track controls
- ✅ Real-time progress updates
- ✅ Responsive design
- ✅ Dark/light theme support

### Limitations

- Requires user to have Spotify Premium for full playback control
- Token expires after 1 hour (automatic refresh not implemented)
- Only shows tracks playing on the authorized account

## Troubleshooting

### "Connect to Spotify" button doesn't work

- Check that your Client ID is correctly set in `spotify-config.js`
- Ensure your redirect URI matches exactly what's configured in your Spotify app
- Check browser console for any error messages

### Authentication works but no track shows

- Make sure you're actively playing music in Spotify
- Check that you have the required scopes enabled
- Verify your Spotify account has the necessary permissions

### Controls don't work

- Spotify Premium is required for playback control
- Ensure the web player is active in your Spotify app

## Security Notes

- Never commit your actual Client ID to public repositories
- Consider using environment variables for production deployments
- The current implementation stores tokens in localStorage (consider more secure alternatives for production)

## Development

To extend the Spotify integration:

1. **Add more API calls**: Check the [Spotify Web API documentation](https://developer.spotify.com/documentation/web-api/)
2. **Implement token refresh**: Add automatic token renewal
3. **Add more controls**: Volume control, shuffle, repeat modes
4. **Show more info**: Recently played tracks, playlists, etc.

## Files Structure

```
public/js/
├── spotify-config.js    # Configuration and authentication
├── spotify.js          # Main Spotify player class
└── main.js            # General site functionality

public/css/
└── styles.css         # Spotify section styling (lines 420-660)

public/index.html      # Spotify section HTML (lines 131-192)
```
