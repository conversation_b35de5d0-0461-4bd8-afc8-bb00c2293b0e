/**
 * Render Deployment Helper Script
 * 
 * This script helps prepare the project for deployment to Render.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Render Deployment Preparation Script');
console.log('--------------------------------------');

// Check if required files exist
console.log('\n📋 Checking required files...');
const requiredFiles = [
  'server.js',
  'package.json',
  'src/config/db.js',
  '.env.production'
];

let allFilesExist = true;
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} exists`);
  } else {
    console.log(`❌ ${file} is missing`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Some required files are missing. Please create them before deploying.');
  process.exit(1);
}

// Check MongoDB URI in .env.production
console.log('\n📋 Checking MongoDB URI in .env.production...');
const envContent = fs.readFileSync('.env.production', 'utf8');
const mongoUriLine = envContent.split('\n').find(line => line.startsWith('MONGODB_URI='));

if (mongoUriLine && mongoUriLine.includes('<username>:<password>')) {
  console.log('⚠️ You need to replace the placeholder MongoDB URI with your actual MongoDB Atlas connection string');
  console.log('Example: MONGODB_URI=mongodb+srv://username:<EMAIL>/portfolio?retryWrites=true&w=majority');
} else if (mongoUriLine) {
  console.log('✅ MongoDB URI is set in .env.production');
} else {
  console.log('❌ MongoDB URI is not set in .env.production');
}

// Deployment instructions
console.log('\n📋 Render Deployment Instructions:');
console.log('1. Create a MongoDB Atlas account and database if you haven\'t already');
console.log('   - Go to https://www.mongodb.com/cloud/atlas');
console.log('   - Create a free cluster');
console.log('   - Create a database user');
console.log('   - Whitelist all IP addresses (0.0.0.0/0)');
console.log('   - Get your connection string and update .env.production');

console.log('\n2. Create a new Web Service on Render:');
console.log('   - Go to https://dashboard.render.com/');
console.log('   - Click "New" and select "Web Service"');
console.log('   - Connect your GitHub repository');
console.log('   - Configure the following settings:');
console.log('     * Name: ouru-dev-backend');
console.log('     * Environment: Node');
console.log('     * Build Command: npm install');
console.log('     * Start Command: npm start');

console.log('\n3. Set the following environment variables in Render:');
console.log('   - NODE_ENV: production');
console.log('   - PORT: 10000');
console.log('   - MONGODB_URI: Your MongoDB Atlas connection string');

console.log('\n4. Deploy your service');
console.log('   - Click "Create Web Service"');

console.log('\n5. Update your frontend API configuration:');
console.log('   - Once deployed, note the URL of your Render service');
console.log('   - Update the BASE_URL in public/js/api-config.js with your Render URL');

console.log('\n✅ Render deployment preparation complete!');
