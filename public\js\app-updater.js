// App Update Manager for ouru.dev PWA
const APP_UPDATER = {
  // Current version of the app - should match the version in service-worker.js
  currentVersion: '1.0.0',

  // URL to check for the latest version (could be an API endpoint)
  // For demo purposes, we're using a simple approach
  // In production, this would be an API endpoint that returns the latest version
  versionCheckInterval: 60 * 60 * 1000, // Check for updates every hour (in milliseconds)

  // Initialize the update checker
  init: function() {
    console.log('App Updater initialized');

    // Check if we have a service worker
    if ('serviceWorker' in navigator) {
      // Check for updates on page load
      this.checkForUpdates();

      // Set up periodic checks for updates
      setInterval(() => this.checkForUpdates(), this.versionCheckInterval);

      // Listen for service worker updates
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        console.log('Service Worker controller changed - new version is active');
      });
    }
  },

  // Check if there's a new version of the service worker
  checkForUpdates: async function() {
    try {
      // First, check if we have a service worker controller
      if (!navigator.serviceWorker.controller) {
        console.log('No active service worker found');
        return;
      }

      // Get the registration
      const registration = await navigator.serviceWorker.getRegistration();

      if (!registration) {
        console.log('No service worker registration found');
        return;
      }

      // Check for updates to the service worker
      await registration.update();

      // If there's a waiting service worker, it means there's an update
      if (registration.waiting) {
        console.log('New service worker version available');
        this.notifyUpdateAvailable();
        return;
      }

      // Also check version.json for app updates
      try {
        // Add a cache-busting parameter to avoid getting cached version
        const response = await fetch('/version.json?_=' + new Date().getTime());
        if (response.ok) {
          const versionData = await response.json();

          // Compare with current version
          if (versionData.version !== this.currentVersion) {
            console.log(`New app version available: ${versionData.version}`);
            this.notifyUpdateAvailable(versionData.notes);
          } else {
            console.log('App is up to date');
          }
        }
      } catch (versionError) {
        console.error('Error checking version.json:', versionError);
      }
    } catch (error) {
      console.error('Error checking for updates:', error);
    }
  },

  // Notify the user that an update is available
  notifyUpdateAvailable: function(updateNotes = '') {
    // Create a notification container if it doesn't exist
    let updateNotification = document.getElementById('update-notification');

    if (!updateNotification) {
      updateNotification = document.createElement('div');
      updateNotification.id = 'update-notification';
      updateNotification.className = 'update-notification';

      // Create the notification content with optional update notes
      const notesHtml = updateNotes ? `<p class="update-notes">${updateNotes}</p>` : '';

      updateNotification.innerHTML = `
        <div class="update-notification-content">
          <p><strong>New Update Available!</strong></p>
          <p>A new version of this app is available.</p>
          ${notesHtml}
          <div class="update-notification-buttons">
            <button id="update-now-btn" class="btn btn-primary">Update Now</button>
            <button id="update-later-btn" class="btn btn-secondary">Later</button>
          </div>
        </div>
      `;

      // Add the notification to the page
      document.body.appendChild(updateNotification);

      // Add event listeners to the buttons
      document.getElementById('update-now-btn').addEventListener('click', () => {
        this.applyUpdate();
        updateNotification.remove();
      });

      document.getElementById('update-later-btn').addEventListener('click', () => {
        updateNotification.remove();
      });

      // Show the notification with animation
      setTimeout(() => {
        updateNotification.classList.add('show');
      }, 100);
    }
  },

  // Apply the update by telling the service worker to skip waiting
  applyUpdate: async function() {
    try {
      const registration = await navigator.serviceWorker.getRegistration();

      if (!registration || !registration.waiting) {
        console.log('No waiting service worker found');
        return;
      }

      // Send a message to the waiting service worker to skip waiting
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });

      // Show a message to the user
      alert('The app is updating. The page will reload to apply the changes.');

      // Reload the page after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('Error applying update:', error);
    }
  }
};

// Initialize the app updater when the page loads
window.addEventListener('load', () => {
  APP_UPDATER.init();
});
