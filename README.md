# Developer Portfolio Website

A fullstack developer portfolio website built with HTML, CSS, Node.js, Express.js, and MongoDB.

## Features

- Responsive design
- Project showcase
- Skills section
- Contact form
- MongoDB database integration
- RESTful API

## Tech Stack

- **Frontend**: HTML, CSS, JavaScript
- **Backend**: Node.js, Express.js
- **Database**: MongoDB
- **Other**: Font Awesome, Google Fonts

## Getting Started

### Prerequisites

- Node.js
- MongoDB

### Installation

1. Clone the repository

   ```
   git clone <repository-url>
   ```

2. Install dependencies

   ```
   cd portfolio-website
   npm install
   ```

3. Create a `.env` file in the root directory and add the following:

   ```
   PORT=5000
   MONGODB_URI=mongodb://localhost:27017/portfolio
   ```

4. Start the development server

   ```
   npm run dev
   ```

5. Open your browser and navigate to `http://localhost:5000`

### Seeding the Database

To populate the database with sample data:

```
node src/config/seeder.js -i
```

To delete all data:

```
node src/config/seeder.js -d
```

## Project Structure

```
portfolio-website/
├── public/                 # Static files
│   ├── css/                # CSS files
│   ├── js/                 # JavaScript files
│   ├── images/             # Image files
│   └── index.html          # Main HTML file
├── src/                    # Server-side code
│   ├── config/             # Configuration files
│   ├── controllers/        # Route controllers
│   ├── models/             # Database models
│   └── routes/             # API routes
├── .env                    # Environment variables
├── package.json            # Project dependencies
├── README.md               # Project documentation
└── server.js               # Main server file
```

## API Endpoints

### Projects

- `GET /api/projects` - Get all projects
- `GET /api/projects/featured` - Get featured projects
- `GET /api/projects/:id` - Get a single project
- `POST /api/projects` - Create a new project
- `PUT /api/projects/:id` - Update a project
- `DELETE /api/projects/:id` - Delete a project

### Skills

- `GET /api/skills` - Get all skills
- `GET /api/skills/category/:category` - Get skills by category
- `POST /api/skills` - Create a new skill
- `PUT /api/skills/:id` - Update a skill
- `DELETE /api/skills/:id` - Delete a skill

### Contact

- `POST /api/contact` - Submit a contact form
- `GET /api/contact` - Get all contact submissions

## Deployment

### Backend Deployment to Render

1. Push your code to GitHub
2. Create a new Web Service in Render
3. Connect your GitHub repository
4. Configure the following settings:

   - **Name**: ouru-dev-backend
   - **Environment**: Node
   - **Build Command**: `npm install`
   - **Start Command**: `npm start`
   - **Environment Variables**:
     - `NODE_ENV`: production
     - `PORT`: 10000
     - `MONGODB_URI`: Your MongoDB connection string

5. Click "Create Web Service"

### Troubleshooting Render Deployment

If you encounter issues with the deployment, check the following:

1. Make sure your MongoDB connection string is correct
2. Verify that all required environment variables are set
3. Check the Render logs for any errors
4. Ensure that your server is configured to serve static files correctly

### Frontend Deployment to Vercel

1. Push your frontend code to a separate GitHub repository
2. Connect your repository to Vercel
3. Configure the build settings as needed
4. Set environment variables for API endpoints

## EmailJS Integration

This project uses EmailJS for handling contact form submissions. To set up EmailJS:

1. Create an account at [EmailJS](https://www.emailjs.com/)
2. Set up an email service (Gmail, Outlook, etc.)
3. Create an email template for contact form submissions
4. Update the EmailJS configuration in the frontend code

See the `EMAILJS_SETUP.md` file for detailed instructions.

## License

This project is licensed under the MIT License.
