/**
 * Render Deployment Helper Script
 * 
 * This script helps prepare the project for deployment to Render by copying necessary files
 * and ensuring the correct structure.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Render Deployment Preparation Script');
console.log('--------------------------------------');

// Check if public directory exists
const publicDir = path.join(__dirname, 'public');
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
  console.log(`✅ Created public directory: ${publicDir}`);
}

// Check if index.html exists in public directory
const indexPath = path.join(publicDir, 'index.html');
if (!fs.existsSync(indexPath)) {
  console.log('Creating basic index.html file...');
  const basicHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ouru.dev</title>
</head>
<body>
  <h1>ouru.dev API Server</h1>
  <p>The API is running. This is a placeholder page.</p>
</body>
</html>`;
  fs.writeFileSync(indexPath, basicHtml);
  console.log(`✅ Created index.html in public directory`);
}

// Check if .env.production exists
const envProdPath = path.join(__dirname, '.env.production');
if (!fs.existsSync(envProdPath)) {
  console.log('❌ .env.production file is missing. Please create it with your production environment variables.');
} else {
  console.log('✅ .env.production file exists');
}

// Deployment instructions
console.log('\n📋 Render Deployment Instructions:');
console.log('1. Make sure your MongoDB Atlas database is properly configured');
console.log('   - Ensure your IP whitelist includes 0.0.0.0/0 for Render access');
console.log('   - Verify your database user credentials are correct');

console.log('\n2. Deploy to Render:');
console.log('   - Go to https://dashboard.render.com/');
console.log('   - Connect your GitHub repository');
console.log('   - Use the render.yaml file for configuration');
console.log('   - Or manually configure with:');
console.log('     * Build Command: npm install');
console.log('     * Start Command: npm start');
console.log('     * Environment Variables from .env.production');

console.log('\n3. Troubleshooting:');
console.log('   - Check Render logs for any errors');
console.log('   - Verify MongoDB connection string is correct');
console.log('   - Ensure public directory and index.html are properly deployed');

console.log('\n✅ Render deployment preparation complete!');
