# Setting Up EmailJS for Contact Form

This guide will walk you through setting up EmailJS to handle the contact form submissions on your portfolio website.

## Step 1: Create an EmailJS Account

1. Go to [EmailJS](https://www.emailjs.com/) and sign up for an account
2. Confirm your email address

## Step 2: Add an Email Service

1. In the EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose your email provider (Gmail, Outlook, etc.)
4. Follow the authentication steps to connect your email account
5. Give your service a name (e.g., "contact_service")
6. Save the service

## Step 3: Create an Email Template

1. In the EmailJS dashboard, go to "Email Templates"
2. Click "Create New Template"
3. Design your email template with the following variables:
   - `{{name}}` - The sender's name
   - `{{email}}` - The sender's email
   - `{{message}}` - The message content
4. Give your template a name (e.g., "contact_form")
5. Save the template

## Step 4: Update Your Website Code

1. The code has been updated with placeholder values that you need to replace with your actual EmailJS credentials:

   In `public/index.html` and `vercel-deploy/public/index.html`:

   ```javascript
   emailjs.init({
     publicKey: "ouru_dev_public_key", // Replace with your actual public key
   });
   ```

   In `public/js/main.js` and `vercel-deploy/public/js/main.js`:

   ```javascript
   const emailjsResult = await emailjs.send(
     "ouru_dev_service", // Replace with your actual service ID
     "contact_form", // Replace with your actual template ID
     {
       name: formData.name,
       email: formData.email,
       message: formData.message,
       subject: "New Contact Form Submission",
     }
   );
   ```

2. You can find your Public Key in the EmailJS dashboard under "Account" > "API Keys"
3. You can find your Service ID in the EmailJS dashboard under "Email Services"
4. You can find your Template ID in the EmailJS dashboard under "Email Templates"

## Step 5: Test the Contact Form

1. Fill out the contact form on your website
2. Submit the form
3. Check your email to see if you received the message
4. Check the browser console for any errors

## Troubleshooting

- If emails are not being sent, check the browser console for error messages
- Verify that your EmailJS account is active and that you haven't exceeded the free tier limits
- Make sure your email service is properly connected
- Check that your template variables match the data being sent

## Additional Resources

- [EmailJS Documentation](https://www.emailjs.com/docs/)
- [EmailJS FAQ](https://www.emailjs.com/docs/faq/can-i-use-emailjs-for-free/)
